apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ../../base
patches:
  - path: deploy.yaml
  - path: sa.yaml
  - path: graphql-publish.yaml
  - patch: |-
      - op: replace
        path: /metadata/name
        value: audit-log-service-graphql-publish-
    target:
      kind: Job
      name: app-cronjob-graphql
      version: v1
      group: batch
images:
  - name: luxurypresence/app
    newName: luxurypresence/audit-log-service
    newTag: v2.4.0 # {"lp-deploy-tag-updater:version": "audit-log-service"}
