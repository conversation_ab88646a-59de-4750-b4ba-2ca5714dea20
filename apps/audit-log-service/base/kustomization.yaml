apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - ../../_baseline/microservice/v1
  - ../../_baseline/job/graphql
  - sa.yaml
  - service.yaml

patches:
  - path: deployment.yaml
  - path: graphql-publish.yaml
  - target:
      group: apps
      version: v1
      kind: Deployment
      name: app-deployment
    patch: |-
      - op: replace
        path: /metadata/name
        value: audit-log-service-deployment
      - op: replace
        path: /spec/template/spec/containers/0/name
        value: audit-log-service
