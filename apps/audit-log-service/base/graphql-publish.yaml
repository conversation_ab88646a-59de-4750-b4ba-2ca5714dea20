apiVersion: batch/v1
kind: Job
metadata:
  name: app-cronjob-graphql
spec:
  template:
    spec:
      containers:
        - name: graphql
          env:
            - name: SERVICE_BACKEND
              value: audit-log-service
            - name: SCHEMA_NAME
              value: audit-log-service
            - name: ENVIRONMENT
              valueFrom:
                configMapKeyRef:
                  name: cluster-info
                  key: APP_ENV
            - name: BASE_DOMAIN
              valueFrom:
                configMapKeyRef:
                  name: cluster-info
                  key: BASE_DOMAIN
            - name: INTERNAL_BASE_DOMAIN
              valueFrom:
                configMapKeyRef:
                  name: cluster-info
                  key: INTERNAL_BASE_DOMAIN
            - name: COSMO_API_KEY
              valueFrom:
                secretKeyRef:
                  name: wundergraph
                  key: COSMO_API_KEY
            - name: WUNDERGRAPH_ROUTER_URL
              valueFrom:
                configMapKeyRef:
                  name: cluster-info
                  key: WUNDERGRAPH_ROUTER_URL
            - name: WUNDERGRAPH_ROUTER_PORT
              valueFrom:
                configMapKeyRef:
                  name: cluster-info
                  key: WUNDERGRAPH_ROUTER_PORT
          resources:
            limits:
              cpu: "500m"
              memory: 200Mi
            requests:
              cpu: "250m"
              memory: 100Mi
